/**
 * RealTimeVoiceService.ts
 *
 * Real-time voice-to-text service using MediaRecorder API for recording
 * and nodejs-whisper for local transcription. Provides real-time audio
 * analysis for visualization while maintaining compatibility with the
 * existing LocalWhisperService interface.
 */

import { EventEmitter } from 'events'
import { promises as fs } from 'fs'
import * as path from 'path'
import * as os from 'os'
import { v4 as uuidv4 } from 'uuid'

// Import types from LocalWhisperService for compatibility
import { LocalWhisperOptions, TranscriptionResult, TranscriptionSegment, RecordingStatus } from './LocalWhisperService'
import PythonStreamingBridge, { StreamingTranscriptionResult } from './PythonStreamingBridge'

// Lazy load nodejs-whisper to avoid startup timeout
let nodewhisper: any = null

const loadWhisperModule = () => {
  if (!nodewhisper) {
    const whisperModule = require('nodejs-whisper')
    nodewhisper = whisperModule.nodewhisper
  }
}

interface RealTimeVoiceOptions extends LocalWhisperOptions {
  enableRealTimeAnalysis?: boolean
  analysisInterval?: number // milliseconds
  enableStreaming?: boolean // Enable real-time streaming transcription
  streamingFallback?: boolean // Fallback to batch processing if streaming fails
}

interface AudioAnalysisData {
  rms: number // Root Mean Square for volume level
  frequency: number[] // Frequency data for visualization
  timestamp: number
}

class RealTimeVoiceService extends EventEmitter {
  private static instance: RealTimeVoiceService
  private isRecording: boolean = false
  private currentFilePath: string | null = null
  private recordingStartTime: number = 0
  private recordingTimer: NodeJS.Timeout | null = null
  private tempDir: string
  private options: RealTimeVoiceOptions
  private audioChunks: Buffer[] = []

  // Streaming transcription components
  private streamingBridge: PythonStreamingBridge | null = null
  private isStreamingMode: boolean = false
  private streamingSessionId: string | null = null
  private streamingInitialized: boolean = false

  private constructor() {
    super()
    this.tempDir = path.join(os.tmpdir(), 'closezly-realtime-voice')
    this.options = {
      modelName: 'base.en',
      maxDuration: 60000, // 60 seconds
      language: 'en',
      wordTimestamps: true,
      enableRealTimeAnalysis: true,
      analysisInterval: 100, // 100ms for smooth visualization
      enableStreaming: true, // Enable streaming by default
      streamingFallback: true // Fallback to batch processing if streaming fails
    }
    this.ensureTempDir()
    this.initializeStreaming()
  }

  public static getInstance(): RealTimeVoiceService {
    if (!RealTimeVoiceService.instance) {
      RealTimeVoiceService.instance = new RealTimeVoiceService()
    }
    return RealTimeVoiceService.instance
  }

  /**
   * Ensure temporary directory exists
   */
  private async ensureTempDir(): Promise<void> {
    try {
      await fs.mkdir(this.tempDir, { recursive: true })
    } catch (error) {
      console.error('[RealTimeVoice] Failed to create temp directory:', error)
    }
  }

  /**
   * Initialize streaming transcription capability
   */
  private async initializeStreaming(): Promise<void> {
    if (!this.options.enableStreaming) {
      console.log('[RealTimeVoice] Streaming disabled by configuration')
      return
    }

    try {
      this.streamingBridge = PythonStreamingBridge.getInstance()

      // Check if streaming is available
      const isAvailable = await this.streamingBridge.isStreamingAvailable()
      if (!isAvailable) {
        console.log('[RealTimeVoice] Streaming not available, will use batch processing')
        this.streamingBridge = null
        return
      }

      // Set up streaming event listeners
      this.setupStreamingEventListeners()

      console.log('[RealTimeVoice] Streaming capability detected and configured')
    } catch (error) {
      console.error('[RealTimeVoice] Error initializing streaming:', error)
      this.streamingBridge = null
    }
  }

  /**
   * Set up event listeners for streaming transcription
   */
  private setupStreamingEventListeners(): void {
    if (!this.streamingBridge) return

    this.streamingBridge.on('interim-result', (result: StreamingTranscriptionResult) => {
      console.log('[RealTimeVoice] Interim result:', result.text)
      this.emit('interim-transcription', result)
    })

    this.streamingBridge.on('final-result', (result: StreamingTranscriptionResult) => {
      console.log('[RealTimeVoice] Final streaming result:', result.text)
      this.emit('transcription-completed', result)
    })

    this.streamingBridge.on('error', (error: Error) => {
      console.error('[RealTimeVoice] Streaming error:', error)
      this.emit('streaming-error', error)

      // Fallback to batch processing if enabled
      if (this.options.streamingFallback && this.isRecording) {
        console.log('[RealTimeVoice] Falling back to batch processing due to streaming error')
        this.isStreamingMode = false
      }
    })

    this.streamingBridge.on('processing-started', () => {
      this.emit('streaming-processing-started')
    })
  }

  /**
   * Check if streaming mode is available and enabled
   */
  public isStreamingAvailable(): boolean {
    return this.streamingBridge !== null && this.options.enableStreaming === true
  }

  /**
   * Get current streaming status
   */
  public getStreamingStatus(): { available: boolean; active: boolean; sessionId: string | null } {
    return {
      available: this.isStreamingAvailable(),
      active: this.isStreamingMode,
      sessionId: this.streamingSessionId
    }
  }

  /**
   * Start real-time voice recording
   * Prepares for receiving real audio data from AudioCaptureService
   */
  public async startRecording(options: RealTimeVoiceOptions = {}): Promise<boolean> {
    if (this.isRecording) {
      console.warn('[RealTimeVoice] Already recording, ignoring start request')
      return false
    }

    try {
      // Merge options with defaults
      this.options = { ...this.options, ...options }

      // Determine if we should use streaming mode
      this.isStreamingMode = this.isStreamingAvailable() && (this.options.enableStreaming === true)

      if (this.isStreamingMode) {
        console.log('[RealTimeVoice] Starting in streaming mode')

        // Initialize streaming bridge if not already done
        if (!this.streamingInitialized && this.streamingBridge) {
          const initialized = await this.streamingBridge.initialize({
            modelSize: this.options.modelName as 'tiny.en' | 'base.en' | 'small.en',
            enableInterimResults: true
          })

          if (!initialized) {
            console.warn('[RealTimeVoice] Failed to initialize streaming, falling back to batch mode')
            this.isStreamingMode = false
          } else {
            this.streamingInitialized = true
          }
        }

        // Start streaming session
        if (this.isStreamingMode && this.streamingBridge) {
          this.streamingSessionId = this.streamingBridge.startSession()
          console.log('[RealTimeVoice] Started streaming session:', this.streamingSessionId)
        }
      }

      if (!this.isStreamingMode) {
        console.log('[RealTimeVoice] Starting in batch processing mode')
      }

      // Ensure AudioCaptureService is running to receive microphone data
      await this.ensureAudioCaptureRunning()

      // Generate unique filename (still needed for fallback)
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const filename = `realtime-voice-${timestamp}-${uuidv4().slice(0, 8)}.wav`
      this.currentFilePath = path.join(this.tempDir, filename)

      // Ensure temp directory exists
      await this.ensureTempDir()

      // Initialize audio accumulation
      this.audioChunks = []

      // Set recording state
      this.isRecording = true
      this.recordingStartTime = Date.now()

      // Set up auto-stop timer
      if (this.options.maxDuration) {
        this.recordingTimer = setTimeout(() => {
          console.log('[RealTimeVoice] Auto-stopping recording due to max duration')
          this.stopRecording()
        }, this.options.maxDuration)
      }

      this.emit('recording-started', {
        streamingMode: this.isStreamingMode,
        sessionId: this.streamingSessionId
      })

      const mode = this.isStreamingMode ? 'streaming' : 'batch'
      console.log(`[RealTimeVoice] Real-time recording started in ${mode} mode, waiting for audio data from microphone`)
      return true

    } catch (error) {
      console.error('[RealTimeVoice] Failed to start recording:', error)
      this.emit('recording-error', error)
      this.cleanup()
      return false
    }
  }

  /**
   * Ensures AudioCaptureService is running to receive microphone data
   */
  private async ensureAudioCaptureRunning(): Promise<void> {
    try {
      // Import AudioCaptureService dynamically to avoid circular dependencies
      const audioService = require('./AudioCaptureService').default

      // Check if already capturing
      const status = audioService.getCaptureStatus()
      if (status.isCapturing) {
        console.log('[RealTimeVoice] AudioCaptureService already running')
        return
      }

      // Start audio capture
      console.log('[RealTimeVoice] Starting AudioCaptureService for voice recording...')
      const started = await audioService.startCapture()
      if (!started) {
        throw new Error('Failed to start AudioCaptureService')
      }

      console.log('[RealTimeVoice] AudioCaptureService started successfully')
    } catch (error) {
      console.error('[RealTimeVoice] Error ensuring AudioCaptureService is running:', error)
      throw error
    }
  }



  /**
   * Receive audio data from renderer process and accumulate it
   */
  public async receiveAudioData(audioBuffer: Buffer): Promise<void> {
    if (!this.isRecording) {
      console.warn('[RealTimeVoice] Received audio data but not recording')
      return
    }

    try {
      // Always accumulate audio chunks for fallback/batch processing
      this.audioChunks.push(audioBuffer)

      // If in streaming mode, send to streaming bridge
      if (this.isStreamingMode && this.streamingBridge) {
        try {
          await this.streamingBridge.sendAudioChunk(audioBuffer)
          console.log(`[RealTimeVoice] Sent audio chunk to streaming bridge (${audioBuffer.length} bytes)`)
        } catch (streamingError) {
          console.error('[RealTimeVoice] Error sending to streaming bridge:', streamingError)

          // Fallback to batch mode if streaming fails
          if (this.options.streamingFallback) {
            console.log('[RealTimeVoice] Falling back to batch processing due to streaming error')
            this.isStreamingMode = false
            this.emit('streaming-fallback', streamingError)
          }
        }
      }

      if (!this.isStreamingMode) {
        console.log(`[RealTimeVoice] Accumulated audio chunk (${audioBuffer.length} bytes), total chunks: ${this.audioChunks.length}`)
      }

      // Emit real-time audio data for visualization if needed
      this.emit('audio-data', audioBuffer)
    } catch (error) {
      console.error('[RealTimeVoice] Error processing audio data:', error)
      this.emit('recording-error', error)
    }
  }

  /**
   * Create a WAV file from accumulated audio chunks
   */
  private async createWavFromChunks(): Promise<void> {
    if (this.audioChunks.length === 0) {
      throw new Error('No audio chunks to process')
    }

    try {
      // Combine all audio chunks
      const combinedAudio = Buffer.concat(this.audioChunks)
      console.log(`[RealTimeVoice] Combined ${this.audioChunks.length} chunks into ${combinedAudio.length} bytes`)

      // Audio parameters (matching the renderer audio capture settings)
      const sampleRate = 16000
      const numChannels = 1
      const bytesPerSample = 2
      const numSamples = combinedAudio.length / bytesPerSample

      // Create WAV file buffer with proper header
      const wavBuffer = Buffer.alloc(44 + combinedAudio.length)

      // WAV header
      wavBuffer.write('RIFF', 0)
      wavBuffer.writeUInt32LE(36 + combinedAudio.length, 4)
      wavBuffer.write('WAVE', 8)
      wavBuffer.write('fmt ', 12)
      wavBuffer.writeUInt32LE(16, 16) // PCM format chunk size
      wavBuffer.writeUInt16LE(1, 20) // PCM format
      wavBuffer.writeUInt16LE(numChannels, 22)
      wavBuffer.writeUInt32LE(sampleRate, 24)
      wavBuffer.writeUInt32LE(sampleRate * numChannels * bytesPerSample, 28)
      wavBuffer.writeUInt16LE(numChannels * bytesPerSample, 32)
      wavBuffer.writeUInt16LE(8 * bytesPerSample, 34)
      wavBuffer.write('data', 36)
      wavBuffer.writeUInt32LE(combinedAudio.length, 40)

      // Copy audio data
      combinedAudio.copy(wavBuffer, 44)

      // Save to file
      if (this.currentFilePath) {
        await fs.writeFile(this.currentFilePath, wavBuffer)
        console.log(`[RealTimeVoice] WAV file created: ${this.currentFilePath} (${wavBuffer.length} bytes)`)
      }
    } catch (error) {
      console.error('[RealTimeVoice] Error creating WAV file from chunks:', error)
      throw error
    }
  }

  /**
   * Stop recording and transcribe using streaming or batch processing
   */
  public async stopRecording(): Promise<TranscriptionResult> {
    if (!this.isRecording) {
      console.warn('[RealTimeVoice] Not recording, ignoring stop request')
      return { success: false, error: 'Not recording' }
    }

    // Prevent multiple stop calls
    const wasRecording = this.isRecording
    const wasStreamingMode = this.isStreamingMode
    this.isRecording = false

    try {
      console.log(`[RealTimeVoice] Stopping recording in ${wasStreamingMode ? 'streaming' : 'batch'} mode...`)

      // Clear timer
      if (this.recordingTimer) {
        clearTimeout(this.recordingTimer)
        this.recordingTimer = null
      }

      const recordingDuration = (Date.now() - this.recordingStartTime) / 1000

      this.emit('recording-stopped', {
        duration: recordingDuration,
        streamingMode: wasStreamingMode
      })

      // Handle streaming mode finalization
      if (wasStreamingMode && this.streamingBridge && this.streamingSessionId) {
        try {
          console.log('[RealTimeVoice] Finalizing streaming session...')
          await this.streamingBridge.finalizeSession()

          // Wait for final result with timeout
          const finalResult = await this.waitForStreamingResult(10000) // 10 second timeout

          if (finalResult) {
            console.log('[RealTimeVoice] Streaming transcription completed successfully')
            await this.cleanup()
            return finalResult
          } else {
            console.warn('[RealTimeVoice] Streaming finalization timeout, falling back to batch processing')
            // Continue to batch processing fallback below
          }
        } catch (streamingError) {
          console.error('[RealTimeVoice] Error during streaming finalization:', streamingError)
          this.emit('streaming-error', streamingError)
          // Continue to batch processing fallback below
        }
      }

      // Batch processing (either as primary mode or fallback)
      return await this.processBatchTranscription(recordingDuration)

    } catch (error) {
      console.error('[RealTimeVoice] Error during stop/transcription:', error)
      await this.cleanup()
      return {
        success: false,
        error: (error as Error).message,
        duration: wasRecording ? (Date.now() - this.recordingStartTime) / 1000 : 0
      }
    }
  }

  /**
   * Wait for streaming transcription result
   */
  private waitForStreamingResult(timeoutMs: number): Promise<TranscriptionResult | null> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        this.streamingBridge?.removeListener('final-result', onFinalResult)
        resolve(null)
      }, timeoutMs)

      const onFinalResult = (result: StreamingTranscriptionResult) => {
        clearTimeout(timeout)
        this.streamingBridge?.removeListener('final-result', onFinalResult)
        resolve(result)
      }

      this.streamingBridge?.once('final-result', onFinalResult)
    })
  }

  /**
   * Process transcription using batch mode (nodejs-whisper)
   */
  private async processBatchTranscription(recordingDuration: number): Promise<TranscriptionResult> {
    console.log('[RealTimeVoice] Processing transcription in batch mode...')

    // Create WAV file from accumulated audio chunks
    if (!this.currentFilePath) {
      throw new Error('No audio file path available')
    }

    // Create WAV file from accumulated chunks
    await this.createWavFromChunks()

    // Check if file exists and has content
    const stats = await fs.stat(this.currentFilePath)
    if (stats.size === 0) {
      throw new Error('Audio file is empty')
    }

    console.log(`[RealTimeVoice] Audio file size: ${(stats.size / 1024).toFixed(2)}KB`)

    console.log('[RealTimeVoice] Starting batch transcription...')
    this.emit('transcription-started')

    // Load nodejs-whisper
    loadWhisperModule()

    // Transcribe with nodejs-whisper
    const transcriptionOptions = {
      modelName: this.options.modelName,
      whisperOptions: {
        language: this.options.language,
        word_timestamps: this.options.wordTimestamps,
        output_format: 'json'
      }
    }

    const result = await nodewhisper(this.currentFilePath, transcriptionOptions)

    console.log('[RealTimeVoice] Batch transcription completed')
    this.emit('transcription-completed')

    // Parse result
    let transcriptionResult: TranscriptionResult

    if (result && result.text) {
      transcriptionResult = {
        success: true,
        text: result.text.trim(),
        duration: recordingDuration,
        segments: result.segments || []
      }
    } else {
      transcriptionResult = {
        success: false,
        error: 'No transcription result',
        duration: recordingDuration
      }
    }

    // Cleanup
    await this.cleanup()

    return transcriptionResult
  }

  /**
   * Check if we should stop AudioCaptureService after voice recording
   * Only stop if no call is active
   */
  private async checkStopAudioCapture(): Promise<void> {
    try {
      // Import AppState to check if call is active
      const appState = require('./AppState').default

      // Don't stop audio capture if a call is active
      const activeCall = appState.getActiveCall()
      if (activeCall.isActive) {
        console.log('[RealTimeVoice] Call is active, keeping AudioCaptureService running')
        return
      }

      // Import AudioCaptureService dynamically
      const audioService = require('./AudioCaptureService').default

      // Stop audio capture since no call is active
      console.log('[RealTimeVoice] Stopping AudioCaptureService after voice recording')
      await audioService.stopCapture()
    } catch (error) {
      console.error('[RealTimeVoice] Error checking/stopping AudioCaptureService:', error)
    }
  }

  /**
   * Cancel recording without transcription
   */
  public async cancelRecording(): Promise<boolean> {
    if (!this.isRecording) {
      return true
    }

    try {
      console.log('[RealTimeVoice] Cancelling recording...')

      // Clear timer
      if (this.recordingTimer) {
        clearTimeout(this.recordingTimer)
        this.recordingTimer = null
      }

      this.isRecording = false
      this.emit('recording-cancelled')

      // Cleanup
      await this.cleanup()

      return true
    } catch (error) {
      console.error('[RealTimeVoice] Error cancelling recording:', error)
      return false
    }
  }

  /**
   * Get current recording status
   */
  public getStatus(): RecordingStatus {
    return {
      isRecording: this.isRecording,
      duration: this.isRecording ? Date.now() - this.recordingStartTime : 0,
      filePath: this.currentFilePath || undefined,
      startTime: this.isRecording ? this.recordingStartTime : undefined
    }
  }

  // Note: Audio analysis and MediaRecorder functionality moved to renderer process
  // This service now focuses on transcription in the main process

  /**
   * Cleanup temporary files and reset state
   */
  private async cleanup(): Promise<void> {
    try {
      // Reset streaming state
      this.isStreamingMode = false
      this.streamingSessionId = null

      // Cleanup file
      if (this.currentFilePath) {
        await fs.unlink(this.currentFilePath).catch(() => {
          // Ignore errors if file doesn't exist
        })
        this.currentFilePath = null
      }

      // Clear accumulated audio chunks
      this.audioChunks = []

      // Check if we should stop AudioCaptureService
      await this.checkStopAudioCapture()
    } catch (error) {
      console.error('[RealTimeVoice] Cleanup error:', error)
    }
  }

  /**
   * Shutdown streaming resources
   */
  public async shutdownStreaming(): Promise<void> {
    try {
      if (this.streamingBridge) {
        await this.streamingBridge.shutdown()
        this.streamingInitialized = false
        console.log('[RealTimeVoice] Streaming bridge shut down')
      }
    } catch (error) {
      console.error('[RealTimeVoice] Error shutting down streaming:', error)
    }
  }

  /**
   * Cleanup old temporary files
   */
  public async cleanupOldFiles(maxAgeMs: number = 3600000): Promise<void> {
    try {
      const files = await fs.readdir(this.tempDir)
      const now = Date.now()

      for (const file of files) {
        if (file.startsWith('realtime-voice-') && (file.endsWith('.webm') || file.endsWith('.wav'))) {
          const filePath = path.join(this.tempDir, file)
          const stats = await fs.stat(filePath)

          if (now - stats.mtime.getTime() > maxAgeMs) {
            await fs.unlink(filePath)
            console.log('[RealTimeVoice] Cleaned up old file:', file)
          }
        }
      }
    } catch (error) {
      console.error('[RealTimeVoice] Error cleaning up old files:', error)
    }
  }
}

export default RealTimeVoiceService
export { RealTimeVoiceOptions, AudioAnalysisData }
