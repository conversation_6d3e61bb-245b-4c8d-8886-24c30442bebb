#!/usr/bin/env node
/**
 * Test script for PythonStreamingBridge
 * Verifies the bridge can communicate with the Python service correctly
 */

const PythonStreamingBridge = require('../../dist/electron/helpers/PythonStreamingBridge').default;

async function testPythonBridge() {
    console.log('🧪 Testing PythonStreamingBridge...\n');
    
    const bridge = PythonStreamingBridge.getInstance();
    let testsPassed = 0;
    let totalTests = 0;
    
    // Test 1: Check streaming availability
    totalTests++;
    console.log('🔄 Test 1: Checking streaming availability...');
    const isAvailable = await bridge.isStreamingAvailable();
    if (isAvailable) {
        console.log('✅ Streaming is available');
        testsPassed++;
    } else {
        console.log('❌ Streaming not available');
        console.log('💡 Make sure Python environment is set up correctly');
        return;
    }
    
    // Test 2: Initialize bridge
    totalTests++;
    console.log('\n🔄 Test 2: Initializing bridge...');
    
    // Set up event listeners
    bridge.on('initialized', () => {
        console.log('📡 Bridge initialized event received');
    });
    
    bridge.on('ready', () => {
        console.log('📡 Bridge ready event received');
    });
    
    bridge.on('interim-result', (result) => {
        console.log('📡 Interim result:', result.text, `(confidence: ${result.confidence})`);
    });
    
    bridge.on('final-result', (result) => {
        console.log('📡 Final result:', result.text, `(confidence: ${result.confidence})`);
        if (result.words) {
            console.log('📡 Word timestamps:', result.words.length, 'words');
        }
    });
    
    bridge.on('error', (error) => {
        console.error('📡 Bridge error:', error.message);
    });
    
    const initialized = await bridge.initialize({
        modelSize: 'tiny.en',
        enableInterimResults: true
    });
    
    if (initialized) {
        console.log('✅ Bridge initialized successfully');
        testsPassed++;
    } else {
        console.log('❌ Bridge initialization failed');
        return;
    }
    
    // Test 3: Check service readiness
    totalTests++;
    console.log('\n🔄 Test 3: Checking service readiness...');
    const isReady = bridge.isServiceReady();
    if (isReady) {
        console.log('✅ Service is ready');
        testsPassed++;
    } else {
        console.log('❌ Service not ready');
    }
    
    // Test 4: Ping test
    totalTests++;
    console.log('\n🔄 Test 4: Ping test...');
    const pingResult = await bridge.ping();
    if (pingResult) {
        console.log('✅ Ping successful');
        testsPassed++;
    } else {
        console.log('❌ Ping failed');
    }
    
    // Test 5: Start session
    totalTests++;
    console.log('\n🔄 Test 5: Starting session...');
    const sessionId = bridge.startSession();
    if (sessionId && typeof sessionId === 'string') {
        console.log('✅ Session started:', sessionId);
        testsPassed++;
    } else {
        console.log('❌ Failed to start session');
    }
    
    // Test 6: Send test audio chunk
    totalTests++;
    console.log('\n🔄 Test 6: Sending test audio chunk...');
    try {
        // Generate a small test audio buffer (silence)
        const testAudioBuffer = Buffer.alloc(1600, 0); // 0.1 seconds of silence at 16kHz
        await bridge.sendAudioChunk(testAudioBuffer);
        console.log('✅ Audio chunk sent successfully');
        testsPassed++;
    } catch (error) {
        console.log('❌ Failed to send audio chunk:', error.message);
    }
    
    // Wait a bit for any interim results
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 7: Finalize session
    totalTests++;
    console.log('\n🔄 Test 7: Finalizing session...');
    try {
        await bridge.finalizeSession();
        console.log('✅ Session finalized successfully');
        testsPassed++;
    } catch (error) {
        console.log('❌ Failed to finalize session:', error.message);
    }
    
    // Wait for final results
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 8: Shutdown
    totalTests++;
    console.log('\n🔄 Test 8: Shutting down bridge...');
    try {
        await bridge.shutdown();
        console.log('✅ Bridge shut down successfully');
        testsPassed++;
    } catch (error) {
        console.log('❌ Failed to shutdown bridge:', error.message);
    }
    
    // Summary
    console.log('\n📊 Test Results:');
    console.log(`✅ Passed: ${testsPassed}/${totalTests} tests`);
    
    if (testsPassed === totalTests) {
        console.log('🎉 All tests passed! PythonStreamingBridge is working correctly.');
        process.exit(0);
    } else {
        console.log('💥 Some tests failed. Check the implementation.');
        process.exit(1);
    }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Run the test
testPythonBridge().catch((error) => {
    console.error('💥 Test failed with error:', error);
    process.exit(1);
});
