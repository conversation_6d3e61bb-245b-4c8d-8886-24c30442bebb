#!/usr/bin/env node
/**
 * Integration test for PythonStreamingBridge with realistic audio simulation
 * Tests the complete pipeline from audio chunks to transcription results
 */

const PythonStreamingBridge = require('../../dist/electron/helpers/PythonStreamingBridge').default;

// Simulate realistic audio chunks (16-bit PCM at 16kHz)
function generateAudioChunk(durationMs = 100, frequency = 440) {
    const sampleRate = 16000;
    const samples = Math.floor(sampleRate * durationMs / 1000);
    const buffer = Buffer.alloc(samples * 2); // 16-bit = 2 bytes per sample
    
    for (let i = 0; i < samples; i++) {
        // Generate a simple sine wave
        const t = i / sampleRate;
        const amplitude = Math.sin(2 * Math.PI * frequency * t) * 0.1; // Low amplitude
        const sample = Math.floor(amplitude * 32767); // Convert to 16-bit
        buffer.writeInt16LE(sample, i * 2);
    }
    
    return buffer;
}

async function testBridgeIntegration() {
    console.log('🧪 Testing PythonStreamingBridge Integration...\n');
    
    const bridge = PythonStreamingBridge.getInstance();
    let interimResults = [];
    let finalResult = null;
    
    // Set up comprehensive event listeners
    bridge.on('initialized', () => {
        console.log('📡 Bridge initialized');
    });
    
    bridge.on('ready', () => {
        console.log('📡 Bridge ready');
    });
    
    bridge.on('processing-started', () => {
        console.log('📡 Processing started');
    });
    
    bridge.on('interim-result', (result) => {
        console.log('📡 Interim result:', {
            text: result.text,
            confidence: result.confidence,
            isInterim: result.isInterim
        });
        interimResults.push(result);
    });
    
    bridge.on('final-result', (result) => {
        console.log('📡 Final result:', {
            text: result.text,
            confidence: result.confidence,
            isInterim: result.isInterim,
            wordCount: result.words ? result.words.length : 0
        });
        finalResult = result;
    });
    
    bridge.on('transcription-completed', (result) => {
        console.log('📡 Transcription completed');
    });
    
    bridge.on('error', (error) => {
        console.error('📡 Bridge error:', error.message);
    });
    
    try {
        // Test 1: Initialize
        console.log('🔄 Step 1: Initializing bridge...');
        const initialized = await bridge.initialize({
            modelSize: 'tiny.en',
            enableInterimResults: true
        });
        
        if (!initialized) {
            throw new Error('Failed to initialize bridge');
        }
        console.log('✅ Bridge initialized\n');
        
        // Test 2: Start session
        console.log('🔄 Step 2: Starting transcription session...');
        const sessionId = bridge.startSession();
        console.log('✅ Session started:', sessionId, '\n');
        
        // Test 3: Send multiple audio chunks to simulate streaming
        console.log('🔄 Step 3: Sending audio chunks...');
        const chunkCount = 10;
        const chunkInterval = 100; // 100ms chunks
        
        for (let i = 0; i < chunkCount; i++) {
            const audioChunk = generateAudioChunk(chunkInterval, 440 + i * 10); // Varying frequency
            await bridge.sendAudioChunk(audioChunk);
            console.log(`📤 Sent audio chunk ${i + 1}/${chunkCount}`);
            
            // Wait between chunks to simulate real-time streaming
            await new Promise(resolve => setTimeout(resolve, 50));
        }
        console.log('✅ All audio chunks sent\n');
        
        // Test 4: Wait for interim results
        console.log('🔄 Step 4: Waiting for interim results...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        if (interimResults.length > 0) {
            console.log(`✅ Received ${interimResults.length} interim results`);
        } else {
            console.log('ℹ️ No interim results (expected for silence/tone)');
        }
        console.log('');
        
        // Test 5: Finalize session
        console.log('🔄 Step 5: Finalizing session...');
        await bridge.finalizeSession();
        
        // Wait for final result
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        if (finalResult) {
            console.log('✅ Final result received');
            console.log('📊 Final transcription details:');
            console.log(`   Text: "${finalResult.text || '(empty)'}"`);
            console.log(`   Confidence: ${finalResult.confidence || 'N/A'}`);
            console.log(`   Success: ${finalResult.success}`);
            console.log(`   Words: ${finalResult.words ? finalResult.words.length : 0}`);
        } else {
            console.log('ℹ️ No final result (expected for silence/tone)');
        }
        console.log('');
        
        // Test 6: Performance check
        console.log('🔄 Step 6: Performance check...');
        const pingStart = Date.now();
        const pingResult = await bridge.ping();
        const pingTime = Date.now() - pingStart;
        
        if (pingResult) {
            console.log(`✅ Ping successful (${pingTime}ms)`);
        } else {
            console.log('❌ Ping failed');
        }
        console.log('');
        
        // Test 7: Cleanup
        console.log('🔄 Step 7: Shutting down...');
        await bridge.shutdown();
        console.log('✅ Bridge shut down successfully\n');
        
        // Summary
        console.log('📊 Integration Test Summary:');
        console.log(`✅ Audio chunks processed: ${chunkCount}`);
        console.log(`✅ Interim results: ${interimResults.length}`);
        console.log(`✅ Final result: ${finalResult ? 'Yes' : 'No'}`);
        console.log(`✅ Ping latency: ${pingTime}ms`);
        console.log('');
        console.log('🎉 Integration test completed successfully!');
        console.log('💡 The bridge is ready for real audio transcription.');
        
        process.exit(0);
        
    } catch (error) {
        console.error('💥 Integration test failed:', error.message);
        
        try {
            await bridge.shutdown();
        } catch (shutdownError) {
            console.error('💥 Error during cleanup:', shutdownError.message);
        }
        
        process.exit(1);
    }
}

// Handle process signals
process.on('SIGINT', async () => {
    console.log('\n🛑 Received interrupt signal, shutting down...');
    try {
        const bridge = PythonStreamingBridge.getInstance();
        await bridge.shutdown();
    } catch (error) {
        console.error('Error during shutdown:', error.message);
    }
    process.exit(0);
});

// Run the integration test
testBridgeIntegration().catch((error) => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
});
