/**
 * PythonStreamingBridge.ts
 *
 * Node.js bridge service that manages communication between the Electron app
 * and the Python streaming transcription service. Handles process lifecycle,
 * audio chunk streaming, and real-time transcription events.
 */

import { EventEmitter } from 'events'
import { spawn, ChildProcess } from 'child_process'
import * as path from 'path'
import * as fs from 'fs'
import { v4 as uuidv4 } from 'uuid'

// Import types for compatibility with existing services
import { TranscriptionResult, TranscriptionSegment } from './LocalWhisperService'

interface PythonStreamingOptions {
  modelSize?: 'tiny.en' | 'base.en' | 'small.en'
  device?: 'cpu' | 'cuda'
  computeType?: 'int8' | 'int16' | 'float16' | 'float32'
  enableInterimResults?: boolean
  processingTimeout?: number // milliseconds
}

interface StreamingTranscriptionResult extends TranscriptionResult {
  isInterim?: boolean
  confidence?: number
  timestamp?: number
  words?: Array<{
    word: string
    start: number
    end: number
    probability: number
  }>
}

interface PythonMessage {
  type: 'interim' | 'final' | 'status' | 'error'
  text?: string
  confidence?: number
  session_id?: string
  timestamp?: number
  status?: string
  message?: string
  words?: Array<{
    word: string
    start: number
    end: number
    probability: number
  }>
}

class PythonStreamingBridge extends EventEmitter {
  private static instance: PythonStreamingBridge
  private pythonProcess: ChildProcess | null = null
  private isInitialized: boolean = false
  private isProcessing: boolean = false
  private currentSessionId: string | null = null
  private options: PythonStreamingOptions
  private pythonPath: string
  private servicePath: string
  private messageQueue: string[] = []
  private isReady: boolean = false
  private initializationTimeout: NodeJS.Timeout | null = null
  private processingTimeout: NodeJS.Timeout | null = null

  private constructor() {
    super()
    this.options = {
      modelSize: 'tiny.en',
      device: 'cpu',
      computeType: 'int8',
      enableInterimResults: true,
      processingTimeout: 30000 // 30 seconds
    }
    
    // Set up paths - handle both development and compiled scenarios
    const isCompiled = __dirname.includes('dist')
    const baseDir = isCompiled
      ? path.join(__dirname, '..', '..', '..') // From dist/electron/helpers back to project root
      : path.join(__dirname, '..', '..') // From electron/helpers back to project root

    this.pythonPath = path.join(baseDir, 'venv-whisper-streaming', 'bin', 'python')
    this.servicePath = path.join(baseDir, 'python-streaming', 'streaming_transcription_service.py')
  }

  public static getInstance(): PythonStreamingBridge {
    if (!PythonStreamingBridge.instance) {
      PythonStreamingBridge.instance = new PythonStreamingBridge()
    }
    return PythonStreamingBridge.instance
  }

  /**
   * Check if streaming capability is available
   */
  public async isStreamingAvailable(): Promise<boolean> {
    try {
      // Check if Python executable exists
      if (!fs.existsSync(this.pythonPath)) {
        console.log('[PythonBridge] Python executable not found:', this.pythonPath)
        return false
      }

      // Check if service script exists
      if (!fs.existsSync(this.servicePath)) {
        console.log('[PythonBridge] Service script not found:', this.servicePath)
        return false
      }

      return true
    } catch (error) {
      console.error('[PythonBridge] Error checking streaming availability:', error)
      return false
    }
  }

  /**
   * Initialize the Python streaming service
   */
  public async initialize(options: PythonStreamingOptions = {}): Promise<boolean> {
    if (this.isInitialized) {
      console.log('[PythonBridge] Already initialized')
      return true
    }

    // Check availability first
    if (!(await this.isStreamingAvailable())) {
      console.error('[PythonBridge] Streaming not available')
      return false
    }

    // Merge options
    this.options = { ...this.options, ...options }

    try {
      console.log('[PythonBridge] Starting Python streaming service...')
      
      // Spawn Python process
      this.pythonProcess = spawn(this.pythonPath, [this.servicePath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      })

      // Set up event handlers
      this.setupProcessHandlers()

      // Wait for initialization
      const initialized = await this.waitForInitialization()
      
      if (initialized) {
        this.isInitialized = true
        console.log('[PythonBridge] Python streaming service initialized successfully')
        this.emit('initialized')
        return true
      } else {
        console.error('[PythonBridge] Failed to initialize Python service')
        this.cleanup()
        return false
      }

    } catch (error) {
      console.error('[PythonBridge] Error initializing Python service:', error)
      this.cleanup()
      return false
    }
  }

  /**
   * Set up process event handlers
   */
  private setupProcessHandlers(): void {
    if (!this.pythonProcess) return

    // Handle stdout (JSON responses)
    this.pythonProcess.stdout?.on('data', (data) => {
      const lines = data.toString().split('\n').filter((line: string) => line.trim())
      
      lines.forEach((line: string) => {
        try {
          const message: PythonMessage = JSON.parse(line)
          this.handlePythonMessage(message)
        } catch (error) {
          console.error('[PythonBridge] Failed to parse JSON response:', line)
        }
      })
    })

    // Handle stderr (logs)
    this.pythonProcess.stderr?.on('data', (data) => {
      const logMessage = data.toString().trim()
      console.log('[PythonBridge] Python log:', logMessage)
    })

    // Handle process exit
    this.pythonProcess.on('close', (code) => {
      console.log(`[PythonBridge] Python process exited with code: ${code}`)
      this.handleProcessExit(code)
    })

    // Handle process errors
    this.pythonProcess.on('error', (error) => {
      console.error('[PythonBridge] Python process error:', error)
      this.emit('error', error)
      this.cleanup()
    })
  }

  /**
   * Handle messages from Python service
   */
  private handlePythonMessage(message: PythonMessage): void {
    console.log('[PythonBridge] Received message:', message.type)

    switch (message.type) {
      case 'status':
        if (message.status === 'ready') {
          this.isReady = true
          this.emit('ready')
        } else if (message.status === 'pong') {
          this.emit('pong')
        }
        break

      case 'interim':
        if (message.text && this.options.enableInterimResults) {
          const result: StreamingTranscriptionResult = {
            success: true,
            text: message.text,
            confidence: message.confidence || 0.5,
            isInterim: true,
            timestamp: message.timestamp
          }
          this.emit('interim-result', result)
        }
        break

      case 'final':
        if (message.text) {
          const result: StreamingTranscriptionResult = {
            success: true,
            text: message.text,
            confidence: message.confidence || 0.5,
            isInterim: false,
            words: message.words,
            timestamp: message.timestamp
          }
          this.emit('final-result', result)
          this.emit('transcription-completed', result)
        }
        this.isProcessing = false
        break

      case 'error':
        console.error('[PythonBridge] Python service error:', message.message)
        this.emit('error', new Error(message.message || 'Unknown Python service error'))
        this.isProcessing = false
        break
    }
  }

  /**
   * Wait for service initialization
   */
  private waitForInitialization(): Promise<boolean> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.error('[PythonBridge] Initialization timeout')
        resolve(false)
      }, 10000) // 10 second timeout

      const onReady = () => {
        clearTimeout(timeout)
        this.removeListener('error', onError)
        resolve(true)
      }

      const onError = () => {
        clearTimeout(timeout)
        this.removeListener('ready', onReady)
        resolve(false)
      }

      this.once('ready', onReady)
      this.once('error', onError)
    })
  }

  /**
   * Send audio chunk to Python service
   */
  public async sendAudioChunk(audioBuffer: Buffer): Promise<void> {
    if (!this.isReady || !this.pythonProcess?.stdin) {
      console.warn('[PythonBridge] Service not ready for audio chunks')
      return
    }

    try {
      // Convert audio buffer to base64
      const audioBase64 = audioBuffer.toString('base64')
      
      // Create message
      const message = {
        type: 'audio_chunk',
        data: audioBase64,
        sample_rate: 16000 // Assuming 16kHz audio
      }

      // Send to Python service
      const jsonMessage = JSON.stringify(message) + '\n'
      this.pythonProcess.stdin.write(jsonMessage)

      if (!this.isProcessing) {
        this.isProcessing = true
        this.emit('processing-started')
      }

    } catch (error) {
      console.error('[PythonBridge] Error sending audio chunk:', error)
      this.emit('error', error)
    }
  }

  /**
   * Finalize current session and get final transcription
   */
  public async finalizeSession(): Promise<void> {
    if (!this.isReady || !this.pythonProcess?.stdin) {
      console.warn('[PythonBridge] Service not ready for finalization')
      return
    }

    try {
      const sessionId = this.currentSessionId || uuidv4()
      
      const message = {
        type: 'finalize',
        session_id: sessionId
      }

      const jsonMessage = JSON.stringify(message) + '\n'
      this.pythonProcess.stdin.write(jsonMessage)

      console.log('[PythonBridge] Session finalization sent')

    } catch (error) {
      console.error('[PythonBridge] Error finalizing session:', error)
      this.emit('error', error)
    }
  }

  /**
   * Start a new transcription session
   */
  public startSession(): string {
    this.currentSessionId = uuidv4()
    this.isProcessing = false
    console.log('[PythonBridge] Started new session:', this.currentSessionId)
    return this.currentSessionId
  }

  /**
   * Check if service is ready
   */
  public isServiceReady(): boolean {
    return this.isReady && this.isInitialized
  }

  /**
   * Send ping to check service health
   */
  public async ping(): Promise<boolean> {
    if (!this.isReady || !this.pythonProcess?.stdin) {
      return false
    }

    try {
      const message = { type: 'ping' }
      const jsonMessage = JSON.stringify(message) + '\n'
      this.pythonProcess.stdin.write(jsonMessage)

      return new Promise((resolve) => {
        const timeout = setTimeout(() => resolve(false), 5000)
        
        this.once('pong', () => {
          clearTimeout(timeout)
          resolve(true)
        })
      })

    } catch (error) {
      console.error('[PythonBridge] Error sending ping:', error)
      return false
    }
  }

  /**
   * Handle process exit
   */
  private handleProcessExit(code: number | null): void {
    console.log('[PythonBridge] Process exited, cleaning up...')
    this.isInitialized = false
    this.isReady = false
    this.isProcessing = false
    this.pythonProcess = null
    this.emit('process-exit', code)
  }

  /**
   * Cleanup resources
   */
  private cleanup(): void {
    if (this.initializationTimeout) {
      clearTimeout(this.initializationTimeout)
      this.initializationTimeout = null
    }

    if (this.processingTimeout) {
      clearTimeout(this.processingTimeout)
      this.processingTimeout = null
    }

    if (this.pythonProcess) {
      try {
        // Send shutdown command
        if (this.pythonProcess.stdin && !this.pythonProcess.killed) {
          const shutdownMessage = JSON.stringify({ type: 'shutdown' }) + '\n'
          this.pythonProcess.stdin.write(shutdownMessage)
        }
        
        // Force kill after timeout
        setTimeout(() => {
          if (this.pythonProcess && !this.pythonProcess.killed) {
            this.pythonProcess.kill('SIGTERM')
          }
        }, 2000)

      } catch (error) {
        console.error('[PythonBridge] Error during cleanup:', error)
      }
    }

    this.isInitialized = false
    this.isReady = false
    this.isProcessing = false
    this.pythonProcess = null
  }

  /**
   * Shutdown the bridge service
   */
  public async shutdown(): Promise<void> {
    console.log('[PythonBridge] Shutting down...')
    this.cleanup()
    this.emit('shutdown')
  }
}

export default PythonStreamingBridge
export { PythonStreamingOptions, StreamingTranscriptionResult }
