#!/usr/bin/env node
/**
 * Node.js test for the Python streaming transcription service
 * This tests the JSON communication protocol between Node.js and Python
 */

const { spawn } = require('child_process');
const path = require('path');

function testStreamingService() {
    console.log('🧪 Testing Python Streaming Service from Node.js...\n');
    
    // Path to Python executable and service script
    const pythonPath = path.join(__dirname, '..', 'venv-whisper-streaming', 'bin', 'python');
    const servicePath = path.join(__dirname, 'streaming_transcription_service.py');
    
    // Spawn Python process
    const pythonProcess = spawn(pythonPath, [servicePath], {
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let responseCount = 0;
    const expectedResponses = 2; // ready + pong
    
    // Handle stdout (JSON responses)
    pythonProcess.stdout.on('data', (data) => {
        const lines = data.toString().split('\n').filter(line => line.trim());
        
        lines.forEach(line => {
            try {
                const response = JSON.parse(line);
                console.log('📨 Received response:', response);
                responseCount++;
                
                if (response.type === 'status' && response.status === 'ready') {
                    console.log('✅ Service is ready, sending ping...');
                    
                    // Send ping message
                    const pingMessage = { type: 'ping' };
                    pythonProcess.stdin.write(JSON.stringify(pingMessage) + '\n');
                }
                
                if (response.type === 'status' && response.status === 'pong') {
                    console.log('✅ Received pong response');
                    
                    // Send shutdown after a brief delay
                    setTimeout(() => {
                        console.log('🔄 Sending shutdown command...');
                        const shutdownMessage = { type: 'shutdown' };
                        pythonProcess.stdin.write(JSON.stringify(shutdownMessage) + '\n');
                    }, 100);
                }
                
            } catch (error) {
                console.error('❌ Failed to parse JSON response:', line);
            }
        });
    });
    
    // Handle stderr (logs)
    pythonProcess.stderr.on('data', (data) => {
        console.log('📋 Service log:', data.toString().trim());
    });
    
    // Handle process exit
    pythonProcess.on('close', (code) => {
        console.log(`\n🏁 Python process exited with code: ${code}`);
        
        if (code === 0 && responseCount >= expectedResponses) {
            console.log('✅ Test completed successfully!');
            console.log(`📊 Received ${responseCount} responses as expected`);
        } else {
            console.log('❌ Test failed');
            console.log(`📊 Expected ${expectedResponses} responses, got ${responseCount}`);
        }
    });
    
    // Handle errors
    pythonProcess.on('error', (error) => {
        console.error('❌ Failed to start Python process:', error);
    });
    
    // Set timeout for test
    setTimeout(() => {
        if (!pythonProcess.killed) {
            console.log('⏰ Test timeout, killing process...');
            pythonProcess.kill();
        }
    }, 15000); // 15 second timeout
}

// Run the test
testStreamingService();
