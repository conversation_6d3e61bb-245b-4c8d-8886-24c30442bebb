#!/usr/bin/env python3
"""
Test script for the streaming transcription service.
This script simulates sending audio chunks and verifies the service responds correctly.
"""

import json
import base64
import numpy as np
import subprocess
import sys
import time
import threading
import queue

def generate_test_audio(duration=2.0, sample_rate=16000, frequency=440):
    """Generate a simple sine wave for testing."""
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    # Generate a simple tone
    audio = np.sin(2 * np.pi * frequency * t) * 0.1
    # Convert to 16-bit PCM
    audio_int16 = (audio * 32767).astype(np.int16)
    return audio_int16.tobytes()

def test_streaming_service():
    """Test the streaming transcription service."""
    print("🧪 Testing Streaming Transcription Service...")
    
    # Start the service process
    service_path = "python-streaming/streaming_transcription_service.py"
    python_path = "venv-whisper-streaming/bin/python"
    
    try:
        process = subprocess.Popen(
            [python_path, service_path],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        print("✅ Service process started")
        
        # Queue for collecting responses
        response_queue = queue.Queue()
        
        # Start thread to read responses
        def read_responses():
            try:
                for line in process.stdout:
                    line = line.strip()
                    if line:
                        try:
                            response = json.loads(line)
                            response_queue.put(response)
                            print(f"📨 Received: {response}")
                        except json.JSONDecodeError:
                            print(f"❌ Invalid JSON response: {line}")
            except Exception as e:
                print(f"❌ Error reading responses: {e}")
        
        response_thread = threading.Thread(target=read_responses)
        response_thread.daemon = True
        response_thread.start()
        
        # Wait for service to be ready
        print("⏳ Waiting for service to initialize...")
        time.sleep(3)
        
        # Test 1: Send ping
        print("\n🔄 Test 1: Ping test")
        ping_message = {"type": "ping"}
        process.stdin.write(json.dumps(ping_message) + "\n")
        process.stdin.flush()
        
        # Wait for pong response
        try:
            response = response_queue.get(timeout=5)
            if response.get("status") == "pong":
                print("✅ Ping test passed")
            else:
                print(f"❌ Unexpected ping response: {response}")
        except queue.Empty:
            print("❌ No response to ping")
        
        # Test 2: Send test audio chunks
        print("\n🔄 Test 2: Audio chunk processing")
        
        # Generate test audio (silence - won't transcribe but tests the pipeline)
        test_audio = generate_test_audio(duration=1.0)
        audio_b64 = base64.b64encode(test_audio).decode('utf-8')
        
        # Send audio chunk
        audio_message = {
            "type": "audio_chunk",
            "data": audio_b64,
            "sample_rate": 16000
        }
        
        process.stdin.write(json.dumps(audio_message) + "\n")
        process.stdin.flush()
        print("✅ Audio chunk sent")
        
        # Test 3: Finalize session
        print("\n🔄 Test 3: Session finalization")
        finalize_message = {
            "type": "finalize",
            "session_id": "test-session-123"
        }
        
        process.stdin.write(json.dumps(finalize_message) + "\n")
        process.stdin.flush()
        print("✅ Finalization message sent")
        
        # Wait for any final responses
        time.sleep(2)
        
        # Test 4: Shutdown
        print("\n🔄 Test 4: Graceful shutdown")
        shutdown_message = {"type": "shutdown"}
        process.stdin.write(json.dumps(shutdown_message) + "\n")
        process.stdin.flush()
        
        # Wait for process to finish
        try:
            process.wait(timeout=10)
            print("✅ Service shut down gracefully")
        except subprocess.TimeoutExpired:
            print("⚠️ Service didn't shut down in time, terminating...")
            process.terminate()
            process.wait()
        
        # Check if there were any errors
        stderr_output = process.stderr.read()
        if stderr_output:
            print(f"\n📋 Service logs:\n{stderr_output}")
        
        print("\n✅ All tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        if 'process' in locals():
            process.terminate()
        return False

def main():
    """Main test function."""
    print("🚀 Starting Streaming Transcription Service Tests\n")
    
    success = test_streaming_service()
    
    if success:
        print("\n🎉 All tests passed! Streaming service is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Tests failed! Check the service implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
