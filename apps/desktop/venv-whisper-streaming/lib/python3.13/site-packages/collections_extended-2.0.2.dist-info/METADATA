Metadata-Version: 2.1
Name: collections-extended
Version: 2.0.2
Summary: Extra Python Collections - bags (multisets) and setlists (ordered sets)
Home-page: https://github.com/mlenzen/collections-extended
License: Apache-2.0
Keywords: collections,bag,multiset,ordered set,unique list
Author: <PERSON>
Author-email: m.le<PERSON><PERSON>@gmail.com
Requires-Python: >=3.7,<4.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Provides-Extra: docs
Provides-Extra: testing
Project-URL: Documentation, https://collections-extended.lenzm.net
Project-URL: Repository, https://github.com/mlenzen/collections-extended
Description-Content-Type: text/x-rst

README
######

.. image:: https://coveralls.io/repos/github/mlenzen/collections-extended/badge.svg?branch=master
	:target: https://coveralls.io/github/mlenzen/collections-extended?branch=master
	:alt: Coverage


.. image:: https://pepy.tech/badge/collections-extended/week
	:target: https://pepy.tech/project/collections-extended/
	:alt: Downloads


Documentation:
	http://collections-extended.lenzm.net/
GitHub:
	https://github.com/mlenzen/collections-extended
PyPI:
	https://pypi.python.org/pypi/collections-extended

Overview
========

``collections_extended`` is a pure Python module with no dependencies providing:

- a ``bag`` class, AKA **multiset**,
- a ``setlist`` class, which is a **unique list** or **ordered set**,
- a ``bijection`` class, ``RangeMap`` which is a mapping from ranges to values and
- a ``IndexedDict`` class, which is an ordered mapping whose elements can be accessed using index, in addition to key.

There are also frozen (hashable) varieties of bags and setlists.

Compatible with and tested against Python 3.6, 3.7, 3.8, 3.9, 3.10 & PyPy3.

Getting Started
===============

.. code-block:: python

	>>> from collections_extended import bag, setlist, bijection, RangeMap, IndexedDict
	>>> from datetime import date
	>>> b = bag('abracadabra')
	>>> b.count('a')
	5
	>>> b.remove('a')
	>>> b.count('a')
	4
	>>> 'a' in b
	True
	>>> b.count('d')
	1
	>>> b.remove('d')
	>>> b.count('d')
	0
	>>> 'd' in b
	False

	>>> sl = setlist('abracadabra')
	>>> sl
	setlist(('a', 'b', 'r', 'c', 'd'))
	>>> sl[3]
	'c'
	>>> sl[-1]
	'd'
	>>> 'r' in sl  # testing for inclusion is fast
	True
	>>> sl.index('d')  # so is finding the index of an element
	4
	>>> sl.insert(1, 'd')  # inserting an element already in raises a ValueError
	Traceback (most recent call last):
	...
		raise ValueError
	ValueError
	>>> sl.index('d')
	4

	>>> bij = bijection({'a': 1, 'b': 2, 'c': 3})
	>>> bij.inverse[2]
	'b'
	>>> bij['a'] = 2
	>>> bij == bijection({'a': 2, 'c': 3})
	True
	>>> bij.inverse[1] = 'a'
	>>> bij == bijection({'a': 1, 'c': 3})
	True

	>>> version = RangeMap()
	>>> version[date(2017, 10, 20): date(2017, 10, 27)] = '0.10.1'
	>>> version[date(2017, 10, 27): date(2018, 2, 14)] = '1.0.0'
	>>> version[date(2018, 2, 14):] = '1.0.1'
	>>> version[date(2017, 10, 24)]
	'0.10.1'
	>>> version[date(2018, 7, 1)]
	'1.0.1'
	>>> version[date(2018, 6, 30):] = '1.0.2'
	>>> version[date(2018, 7, 1)]
	'1.0.2'

	>>> idict = IndexedDict()
	>>> idict['a'] = "A"
	>>> idict['b'] = "B"
	>>> idict['c'] = "C"
	>>> idict.get(key='a')
	'A'
	>>> idict.get(index=2)
	'C'
	>>> idict.index('b')
	1

Installation
============

``pip install collections-extended``

Usage
=====
	``from collections_extended import bag, frozenbag, setlist, frozensetlist, bijection``

Classes
=======
There are seven new collections provided:

Bags
----
bag
	This is a bag AKA multiset.
frozenbag
	This is a frozen (hashable) version of a bag.

Setlists
--------
setlist
	An ordered set or a list of unique elements depending on how you look at it.
frozensetlist
	This is a frozen (hashable) version of a setlist.

Mappings
--------
bijection
	A one-to-one mapping.
RangeMap
	A mapping from ranges (of numbers/dates/etc)
IndexedDict
	A mapping that keeps insertion order and allows access by index.

Python 2
--------

The package no longer supports Python 2. The last version to support
Python 2.7, 3.4 & 3.5 was 1.0. No new feature releases will be done for 1.x but
any significant bugs that come up may be fixed.

:Author: Michael Lenzen
:Copyright: 2021 Michael Lenzen
:License: Apache License, Version 2.0
:Project Homepage: https://github.com/mlenzen/collections-extended

