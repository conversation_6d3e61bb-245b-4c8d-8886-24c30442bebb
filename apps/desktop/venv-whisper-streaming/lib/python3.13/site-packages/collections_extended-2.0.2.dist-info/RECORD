collections_extended-2.0.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
collections_extended-2.0.2.dist-info/LICENSE,sha256=c7p036pSC0mkAbXSFFmoUjoUbzt1GKgz7qXvqFEwv2g,10273
collections_extended-2.0.2.dist-info/METADATA,sha256=_7c3i30-qhH-4iWrC2G8_prHPQ3p2W496NfMHHq1bCo,5028
collections_extended-2.0.2.dist-info/RECORD,,
collections_extended-2.0.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
collections_extended-2.0.2.dist-info/WHEEL,sha256=y3eDiaFVSNTPbgzfNn0nYn5tEn1cX6WrdetDlQM4xWw,83
collections_extended/__init__.py,sha256=JkDaOAWvNwFzNCW6Hi9GB0surpCLk9I516TZDnrRtr0,1375
collections_extended/__pycache__/__init__.cpython-313.pyc,,
collections_extended/__pycache__/_util.cpython-313.pyc,,
collections_extended/__pycache__/_version.cpython-313.pyc,,
collections_extended/__pycache__/bags.cpython-313.pyc,,
collections_extended/__pycache__/bijection.cpython-313.pyc,,
collections_extended/__pycache__/indexed_dict.cpython-313.pyc,,
collections_extended/__pycache__/range_map.cpython-313.pyc,,
collections_extended/__pycache__/sentinel.cpython-313.pyc,,
collections_extended/__pycache__/setlists.cpython-313.pyc,,
collections_extended/_util.py,sha256=Aztg_9FYH2C674vMphcbHBdC-UIrpVJwSjXJFXil9qM,1788
collections_extended/_version.py,sha256=JjFttv5fcI7KHcRsIM5kc-xd20DKxq5eUD7sncTy6OQ,92
collections_extended/bags.py,sha256=7hpmnny6sasUz9EnEcSt-VcTkTbExqyiuBjphywhTbI,14133
collections_extended/bijection.py,sha256=7l021yknjt2XwGBvdRYO7di37dbLEp6YDFRLML3bk8I,2262
collections_extended/indexed_dict.py,sha256=dFeoG2xJ5YvRf1hBRmn613l7HF28YoN5fe1Jo3NGK5Q,10314
collections_extended/range_map.py,sha256=OAAk-DLX8gUqQ8hJ_kCGcRL8RXabdg_5iQT9Up6Crfg,11122
collections_extended/sentinel.py,sha256=KR-bvbCtB_eHsdcWUbXxMQ_OTeAzTm743XpblOIraKU,853
collections_extended/setlists.py,sha256=87liu4pZ8DJEaDtwHidJyOZ4QPAdPzfzy9AwAV084Yw,15021
